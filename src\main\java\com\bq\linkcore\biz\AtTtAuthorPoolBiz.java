package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorProfileHistoryDO;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorPoolMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorProfileHistoryMapper;
import com.bq.linkcore.utils.DateTimeUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Component
public class AtTtAuthorPoolBiz {

    @Resource
    private AtTiktokAuthorPoolMapper tiktokAuthorPoolMapper;

    @Resource
    private AtTiktokAuthorProfileHistoryMapper tiktokAuthorProfileRecordMapper;

    /**
     * 插入TikTok作者池记录
     * @param authorPoolDO TikTok作者池实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorPool(AtTiktokAuthorPoolDO authorPoolDO) {
        return tiktokAuthorPoolMapper.insert(authorPoolDO);
    }

    /**
     * 根据uniqueId查询TikTok作者池记录
     * @param uniqueId 用户名
     * @return TikTok作者池实体对象，不存在则返回null
     */
    public AtTiktokAuthorPoolDO queryAuthorPoolByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorPoolDO>()
                .eq(AtTiktokAuthorPoolDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorPoolDO::getIsDel, 0);
        return tiktokAuthorPoolMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId查询记录是否存在
     * @param uniqueId 用户名
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorPoolExistsByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorPoolDO>()
                .eq(AtTiktokAuthorPoolDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorPoolDO::getIsDel, 0);
        return tiktokAuthorPoolMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新TikTok作者池记录
     * @param authorPoolDO TikTok作者池实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorPool(AtTiktokAuthorPoolDO authorPoolDO) {
        return tiktokAuthorPoolMapper.updateById(authorPoolDO);
    }

    public List<AtTiktokAuthorPoolDO> queryUpdateNext(){
        LocalDateTime localDateTime = DateTimeUtil.generateMinTime(LocalDateTime.now());

        LambdaQueryWrapper<AtTiktokAuthorPoolDO> wrapper = new  LambdaQueryWrapper<AtTiktokAuthorPoolDO>()
                .eq(AtTiktokAuthorPoolDO::getIsDel, 0)
//                .le(AtTiktokAuthorPoolDO::getUpdateTime, localDateTime)
                ;
        return  tiktokAuthorPoolMapper.selectList(wrapper);
    }

    // ==================== AtTiktokAuthorProfileRecord 相关方法 ====================

    /**
     * 插入TikTok作者主页记录
     * @param authorProfileRecordDO TikTok作者主页记录实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorProfileRecord(AtTiktokAuthorProfileHistoryDO authorProfileRecordDO) {
        return tiktokAuthorProfileRecordMapper.insert(authorProfileRecordDO);
    }

    /**
     * 根据uniqueId和recordDay查询TikTok作者主页记录
     * @param uniqueId 用户名
     * @param recordDay 数据获取日期
     * @return TikTok作者主页记录实体对象，不存在则返回null
     */
    public AtTiktokAuthorProfileHistoryDO queryAuthorProfileRecordByUniqueIdAndRecordDay(String uniqueId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO>()
                .eq(AtTiktokAuthorProfileHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorProfileHistoryDO::getRecordDay, recordDay)
                .eq(AtTiktokAuthorProfileHistoryDO::getIsDel, 0);
        return tiktokAuthorProfileRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId查询TikTok作者主页记录列表
     * @param uniqueId 用户名
     * @return TikTok作者主页记录列表
     */
    public List<AtTiktokAuthorProfileHistoryDO> queryAuthorProfileRecordListByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO>()
                .eq(AtTiktokAuthorProfileHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorProfileHistoryDO::getIsDel, 0)
                .orderByDesc(AtTiktokAuthorProfileHistoryDO::getRecordDay);
        return tiktokAuthorProfileRecordMapper.selectList(queryWrapper);
    }

    /**
     * 根据uniqueId和recordDay查询记录是否存在
     * @param uniqueId 用户名
     * @param recordDay 数据获取日期
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorProfileRecordExistsByUniqueIdAndRecordDay(String uniqueId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileHistoryDO>()
                .eq(AtTiktokAuthorProfileHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorProfileHistoryDO::getRecordDay, recordDay)
                .eq(AtTiktokAuthorProfileHistoryDO::getIsDel, 0);
        return tiktokAuthorProfileRecordMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新TikTok作者主页记录
     * @param authorProfileRecordDO TikTok作者主页记录实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorProfileRecord(AtTiktokAuthorProfileHistoryDO authorProfileRecordDO) {
        return tiktokAuthorProfileRecordMapper.updateById(authorProfileRecordDO);
    }

}
