package com.bq.linkcore.services.impl.monitor.provider.tiktok;

import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.client.tikhub.models.TikHubArticle;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * TikTok监控提供者
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
public class TiktokMonitoringProvider {

    private final static String TIKTOK_THREAD_TAG = "tiktok-provider";

    /**
     * TikTok专用线程池
     */
    private final WorkThreadPool tiktokThreadPool = new WorkThreadPool(
            2,
            8,
            2L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(5000),
            new WorkThreadFactory(TIKTOK_THREAD_TAG),
            new ThreadRejectPolicy(TIKTOK_THREAD_TAG)
    );

    @Resource
    private TikHubTiktokAccountRequester accountRequester;
    @Resource
    private TiktokDataStorageHelper tiktokDataStorageService;

    /**
     * 处理用户列表 - 批量处理作品主页数据更新
     */
    public void processAuthorAndWorksBatch(List<String> uniqueIds) {
        log.info("TikTok提供者开始处理用户，数量: {}", uniqueIds.size());

        for (String uniqueId : uniqueIds) {
            tiktokThreadPool.addTask(() -> {
                try {
                    processUserInternal(uniqueId);
                } catch (Exception e) {
                    log.error("TikTok提供者处理用户异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }


    /**
     * 处理达人主页数据更新或者新增
     */
    public void processAuthorProfilesBatch(List<String> uniqueIds) {
        log.info("TikTok提供者开始处理作者信息，数量: {}", uniqueIds.size());

        for (String uniqueId : uniqueIds) {
            tiktokThreadPool.addTask(() -> {
                try {
                    processAuthorProfileInternal(uniqueId);
                } catch (Exception e) {
                    log.error("TikTok提供者处理作者信息异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }

    /**
     * 批量处理用户作品数据更新
     */
    public void processWorksBatch(List<String> uniqueIds) {
        log.info("TikTok提供者开始处理用户作品，数量: {}", uniqueIds.size());

        for (String uniqueId : uniqueIds) {
            tiktokThreadPool.addTask(() -> {
                try {
                    processAuthorWorksInternal(uniqueId, null);
                } catch (Exception e) {
                    log.error("TikTok提供者处理用户作品异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }

    /**
     * 内部用户处理逻辑
     */
    private void processUserInternal(String uniqueId) {
        log.debug("开始处理TikTok用户: {}", uniqueId);

        // 1. 获取用户基本信息
        AuthorInfo userProfile = accountRequester.getUserProfileWebV1(uniqueId, null);
        if (userProfile != null) {
            tiktokDataStorageService.saveUserProfile(userProfile);
        }

        // 2. 获取用户作品 - 使用现有的TiktokAuthorWorkService
        processAuthorWorks(uniqueId, null);

        log.debug("完成处理TikTok用户: {}", uniqueId);
    }

    /**
     * 作者主页数据 - 单个达人
     */
    private void processAuthorProfileInternal(String uniqueId) {
        log.debug("开始处理TikTok作者信息: {}", uniqueId);

        AuthorInfo userProfile = accountRequester.getUserProfileWebV1(uniqueId, null);
        if (userProfile != null) {
            tiktokDataStorageService.saveUserProfile(userProfile);
            log.debug("成功保存TikTok作者信息: {}", uniqueId);
        } else {
            log.warn("未获取到TikTok作者信息: {}", uniqueId);
        }
    }

    /**
     * 批量处理用户
     */
    public void batchProcessUsers(List<String> uniqueIds, int batchSize) {
        log.info("TikTok提供者开始批量处理用户，总数量: {}, 批次大小: {}", uniqueIds.size(), batchSize);

        for (int i = 0; i < uniqueIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, uniqueIds.size());
            List<String> batch = uniqueIds.subList(i, endIndex);

            tiktokThreadPool.addTask(() -> {
                try {
                    processBatchInternal(batch);
                } catch (Exception e) {
                    log.error("TikTok提供者批量处理异常，{}", endIndex, e);
                }
            });
            try {
                Thread.sleep(1000);
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 内部批次处理逻辑
     */
    private void processBatchInternal(List<String> batch) {
        log.debug("开始处理TikTok批次，数量: {}", batch.size());

        for (String uniqueId : batch) {
            try {
                processUserInternal(uniqueId);

                // 添加延迟避免频繁请求
                Thread.sleep(800);

            } catch (Exception e) {
                log.error("批次处理单个用户异常，uniqueId: {}", uniqueId, e);
            }
        }

        log.debug("完成处理TikTok批次，数量: {}", batch.size());
    }

    /**
     * 处理特定用户的作品监控
     */
    public void processAuthorWorks(String uniqueId, String secUid) {
        tiktokThreadPool.addTask(() -> {
            try {
                log.debug("开始TikTok作品监控: {}", uniqueId);
                processAuthorWorksInternal(uniqueId, secUid);
            } catch (Exception e) {
                log.error("TikTok作品监控异常，uniqueId: {}", uniqueId, e);
            }
        });
    }



    /**
     * 内部接口实现 获取作品
     * @param uniqueId
     * @param secUid
     */
    private void processAuthorWorksInternal(String uniqueId, String secUid) {
        List<TikHubArticle> tikHubArticleList = getArticleDetailsList(uniqueId, secUid);

        if (CollectionUtils.isEmpty(tikHubArticleList)) {
            log.warn("未获取到作品数据，uniqueId: {}, secUid: {}", uniqueId, secUid);
            return;
        }

        log.info("获取到作品数据 {} 条，uniqueId: {}", tikHubArticleList.size(), uniqueId);

        String recordDay = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        for (TikHubArticle tikHubArticle : tikHubArticleList) {
            try {
                AtTiktokAuthorWorkRecordDO workRecordDO = tiktokDataStorageService.processWorkRecord(tikHubArticle, uniqueId);
                tiktokDataStorageService.processWorkHistory(workRecordDO, recordDay);
            } catch (Exception e) {
                log.error("处理单个作品数据异常，workId: {}, uniqueId: {}",
                        tikHubArticle.getWorkId(), uniqueId, e);
            }
        }

        log.info("完成处理作者作品数据，uniqueId: {}, 处理数量: {}", uniqueId, tikHubArticleList.size());
    }
    /**
     * 获取作品详情（包含mock数据）
     */
    private List<TikHubArticle> getArticleDetailsList(String uniqueId, String secUid) {
        try {
            // 调用真实API（目前返回空列表）
            List<TikHubArticle> realData = accountRequester.getUserArticlesByWebV1(uniqueId, secUid, 50);

            // 如果真实API有数据，直接返回
            if (realData != null && !realData.isEmpty()) {
                return realData;
            }

            return null;

        } catch (Exception e) {
            log.error("获取作品详情异常，uniqueId: {}, secUid: {}", uniqueId, secUid, e);
            // 异常情况下也返回mock数据
            return null;
        }
    }


    /**
     * 停止提供者
     */
    public void shutdown() {
        log.info("开始停止TikTok监控提供者");
        tiktokThreadPool.stop();
        log.info("TikTok监控提供者已停止");
    }
}
