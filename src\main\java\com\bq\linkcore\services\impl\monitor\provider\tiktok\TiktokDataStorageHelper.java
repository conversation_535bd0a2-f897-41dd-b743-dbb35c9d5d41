package com.bq.linkcore.services.impl.monitor.provider.tiktok;

import com.bq.linkcore.bean.entity.*;
import com.bq.linkcore.biz.AtTtAuthorPoolBiz;
import com.bq.linkcore.biz.AtTtAuthorWorkBiz;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.client.tikhub.models.TikHubArticle;
import com.bq.linkcore.client.tikhub.models.TikHubArticleMusic;
import com.bq.linkcore.client.tikhub.models.TikHubArticleVideo;
import com.bq.linkcore.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * TikTok数据存储服务 - 使用现有Biz类
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Component
public class TiktokDataStorageHelper {

    @Resource
    private AtTtAuthorPoolBiz atTtAuthorPoolBiz;

    @Resource
    private AtTtAuthorWorkBiz atTtAuthorWorkBiz;
    /**
     * 保存用户基本信息
     */
    public void saveUserProfile(AuthorInfo userProfile) {
        log.debug("保存TikTok用户信息: {}", userProfile.getUniqueId());

        try {
            // 1. 保存到作者池表
            saveToAuthorPool(userProfile);

            // 2. 保存到作者历史记录表
            saveToAuthorProfileRecord(userProfile);

            log.debug("成功保存TikTok用户信息: {}", userProfile.getUniqueId());

        } catch (Exception e) {
            log.error("保存TikTok用户信息异常，uniqueId: {}", userProfile.getUniqueId(), e);
        }
    }

    /**
     * 保存到作者池表
     */
    @Transactional(readOnly = false, rollbackFor = {RuntimeException.class, Exception.class})
    private void saveToAuthorPool(AuthorInfo userProfile) {
        // 先查询是否存在
        AtTiktokAuthorPoolDO existingPool = atTtAuthorPoolBiz.queryAuthorPoolByUniqueId(userProfile.getUniqueId());

        if (existingPool != null) {
            AtTiktokAuthorPoolDO updatePool = buildAuthorPoolDO(userProfile);
            updatePool.setId(existingPool.getId());
            updatePool.setCreator(existingPool.getCreator());
            updatePool.setCreateTime(existingPool.getCreateTime());
            updatePool.setUpdateTime(LocalDateTime.now());

            atTtAuthorPoolBiz.updateAuthorPool(updatePool);
            log.debug("更新TikTok作者池记录: {}", userProfile.getUniqueId());
        } else {
            // 插入新记录
            AtTiktokAuthorPoolDO newPool = buildAuthorPoolDO(userProfile);
            atTtAuthorPoolBiz.insertAuthorPool(newPool);
            log.debug("插入TikTok作者池记录: {}", userProfile.getUniqueId());
        }
    }

    /**
     * 保存到作者历史记录表
     */
    @Transactional(readOnly = false, rollbackFor = {RuntimeException.class, Exception.class})
    private void saveToAuthorProfileRecord(AuthorInfo userProfile) {
        String recordDay = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 先查询今天是否已有记录
        AtTiktokAuthorProfileHistoryDO existingRecord = atTtAuthorPoolBiz
                .queryAuthorProfileRecordByUniqueIdAndRecordDay(userProfile.getUniqueId(), recordDay);

        if (existingRecord != null) {
            // 更新今天的记录
            AtTiktokAuthorProfileHistoryDO updateRecord = buildAuthorProfileRecordDO(userProfile, recordDay);
            updateRecord.setId(existingRecord.getId());
            updateRecord.setCreator(existingRecord.getCreator());
            updateRecord.setCreateTime(existingRecord.getCreateTime());
            updateRecord.setUpdateTime(LocalDateTime.now());

            atTtAuthorPoolBiz.updateAuthorProfileRecord(updateRecord);
            log.debug("更新TikTok作者历史记录: {}, 日期: {}", userProfile.getUniqueId(), recordDay);
        } else {
            // 插入新的历史记录
            AtTiktokAuthorProfileHistoryDO newRecord = buildAuthorProfileRecordDO(userProfile, recordDay);
            atTtAuthorPoolBiz.insertAuthorProfileRecord(newRecord);
            log.debug("插入TikTok作者历史记录: {}, 日期: {}", userProfile.getUniqueId(), recordDay);
        }
    }

    /**
     * 处理作品record
     * @param tikHubArticle
     * @param uniqueId
     * @return
     */
    @Transactional(readOnly = false, rollbackFor = {RuntimeException.class, Exception.class})
    public AtTiktokAuthorWorkRecordDO processWorkRecord(TikHubArticle tikHubArticle, String uniqueId) {
        TikHubArticleVideo tikHubArticleVideo = tikHubArticle.getVideo();
        // 插入video
        TiktokArticleVideosDO articleVideosDO = atTtAuthorWorkBiz.queryArticleVideo(tikHubArticleVideo.getVideoID());
        if (articleVideosDO != null) {
            atTtAuthorWorkBiz.insertArticleVideo(buildArticleVideoDO(tikHubArticleVideo));
        }

        // 插入music
        TikHubArticleMusic tikHubArticleMusic = tikHubArticle.getMusic();
        // 插入video
        TiktokArticleMusicsDO articleMusicDO = atTtAuthorWorkBiz.queryArticleMusic(tikHubArticleMusic.getMusicId());
        if (articleMusicDO != null) {
            atTtAuthorWorkBiz.insertArticleMusic(buildArticleMusicDO(tikHubArticleMusic));
        }

        String workId = tikHubArticle.getWorkId();
        // 先查询是否存在
        AtTiktokAuthorWorkRecordDO existingRecord = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkId(workId);
        if (existingRecord != null) {
            log.debug("作品记录已存在，跳过插入，workId: {}", workId);
            return existingRecord;
        }

        // 构建记录对象
        AtTiktokAuthorWorkRecordDO recordDO = buildWorkRecordDO(tikHubArticle, uniqueId);
        // 插入数据
        int result = atTtAuthorWorkBiz.insertAuthorWorkRecord(recordDO);
        if (result > 0) {
            log.debug("成功插入作品记录，workId: {}", workId);
        } else {
            log.warn("插入作品记录失败，workId: {}", workId);
        }

        return recordDO;
    }

    /**
     *  处理历史数据
     * @param authorWorkRecordDO
     * @param recordDay
     */
    @Transactional(readOnly = false, rollbackFor = {RuntimeException.class, Exception.class})
    public void processWorkHistory(AtTiktokAuthorWorkRecordDO authorWorkRecordDO, String recordDay) {
        String workId = authorWorkRecordDO.getWorkId();

        // 先查询是否存在
        AtTiktokAuthorWorkHistoryDO existingHistory =
                atTtAuthorWorkBiz.queryAuthorWorkHistoryByWorkIdAndRecordDay(workId, recordDay);
        if (existingHistory != null) {
            AtTiktokAuthorWorkHistoryDO n = buildWorkHistoryDO(authorWorkRecordDO, recordDay);
            n.setId(existingHistory.getId());
            n.setCreator(existingHistory.getCreator());
            n.setCreateTime(existingHistory.getCreateTime());
            n.setUpdateTime(LocalDateTime.now());

            atTtAuthorWorkBiz.updateAuthorWorkHistory(n);
            return;
        }

        AtTiktokAuthorWorkHistoryDO historyDO = buildWorkHistoryDO(authorWorkRecordDO, recordDay);

        int result = atTtAuthorWorkBiz.insertAuthorWorkHistory(historyDO);
        if (result > 0) {
            log.debug("成功插入作品历史记录，workId: {}, recordDay: {}", workId, recordDay);
        } else {
            log.warn("插入作品历史记录失败，workId: {}, recordDay: {}", workId, recordDay);
        }
    }

    /**
     * 构建作品记录DO对象
     */
    private AtTiktokAuthorWorkRecordDO buildWorkRecordDO(TikHubArticle tikHubArticle, String uniqueId) {
        AtTiktokAuthorWorkRecordDO recordDO = new AtTiktokAuthorWorkRecordDO();

        // 复制基本属性
        BeanUtils.copyProperties(tikHubArticle, recordDO);

        // 设置特定字段
        recordDO.setUniqueId(uniqueId);
        recordDO.setImages(StringUtil.arrayToStr(tikHubArticle.getImgUrls()));
        recordDO.setHashtags(StringUtil.arrayToStr(tikHubArticle.getHashTags()));

        recordDO.setMusicId(tikHubArticle.getMusic().getMusicId());
        recordDO.setVideoId(tikHubArticle.getVideo().getVideoID());

        recordDO.setCreator(0L);
        recordDO.setUpdater(0L);
        recordDO.setCreateTime(LocalDateTime.now());
        recordDO.setUpdateTime(LocalDateTime.now());

        return recordDO;
    }


    private TiktokArticleVideosDO buildArticleVideoDO(TikHubArticleVideo tikHubArticleVideo) {
        TiktokArticleVideosDO videosDO = new TiktokArticleVideosDO();

        // 复制基本属性
        BeanUtils.copyProperties(tikHubArticleVideo, videosDO);

        return videosDO;
    }

    private TiktokArticleMusicsDO buildArticleMusicDO(TikHubArticleMusic tikHubArticleMusic) {
        TiktokArticleMusicsDO musicsDO = new TiktokArticleMusicsDO();

        // 复制基本属性
        BeanUtils.copyProperties(tikHubArticleMusic, musicsDO);

        return musicsDO;
    }

    /**
     * 构建作品历史记录DO对象
     */
    private AtTiktokAuthorWorkHistoryDO buildWorkHistoryDO(AtTiktokAuthorWorkRecordDO authorWorkRecordDO, String recordDay) {
        AtTiktokAuthorWorkHistoryDO historyDO = new AtTiktokAuthorWorkHistoryDO();

        // 复制基本属性
        BeanUtils.copyProperties(authorWorkRecordDO, historyDO);

        // 设置特定字段
        historyDO.setRecordDay(recordDay);
        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setUpdateTime(LocalDateTime.now());

        return historyDO;
    }


    /**
     * 构建作者池DO对象
     */
    private AtTiktokAuthorPoolDO buildAuthorPoolDO(AuthorInfo userProfile) {
        AtTiktokAuthorPoolDO poolDO = new AtTiktokAuthorPoolDO();

        // 复制基本属性
        BeanUtils.copyProperties(userProfile, poolDO);

        // 设置特定字段
        poolDO.setCreator(0L);
        poolDO.setUpdater(0L);
        poolDO.setCreateTime(LocalDateTime.now());
        poolDO.setUpdateTime(LocalDateTime.now());
        poolDO.setIsDel(0);

        return poolDO;
    }

    /**
     * 构建作者历史记录DO对象
     */
    private AtTiktokAuthorProfileHistoryDO buildAuthorProfileRecordDO(AuthorInfo userProfile, String recordDay) {
        AtTiktokAuthorProfileHistoryDO recordDO = new AtTiktokAuthorProfileHistoryDO();

        // 复制基本属性
        BeanUtils.copyProperties(userProfile, recordDO);

        // 设置特定字段
        recordDO.setRecordDay(recordDay);
        recordDO.setRefreshTime(LocalDateTime.now());
        recordDO.setCreator(0L);
        recordDO.setUpdater(0L);
        recordDO.setCreateTime(LocalDateTime.now());
        recordDO.setUpdateTime(LocalDateTime.now());
        recordDO.setIsDel(0);

        return recordDO;
    }


    /**
     * 保存用户作品列表 - 委托给TiktokAuthorWorkService处理
     */
    public void saveUserWorks(List<TikHubArticle> tikHubArticles, String uniqueId) {
        if (tikHubArticles == null || tikHubArticles.isEmpty()) {
            return;
        }

        log.debug("TikTok用户作品将由TiktokAuthorWorkService处理: {}, 数量: {}", uniqueId, tikHubArticles.size());
        // 插入作品数据
    }

    /**
     * 批量保存用户信息
     */
    public void batchSaveUserProfiles(List<AuthorInfo> userProfiles) {
        if (userProfiles == null || userProfiles.isEmpty()) {
            return;
        }

        log.debug("批量保存TikTok用户信息，数量: {}", userProfiles.size());

        try {
            for (AuthorInfo profile : userProfiles) {
                saveUserProfile(profile);

                Thread.sleep(100);
            }

            log.debug("成功批量保存TikTok用户信息，数量: {}", userProfiles.size());

        } catch (Exception e) {
            log.error("批量保存TikTok用户信息异常", e);
        }
    }

    /**
     * 检查用户是否存在
     */
    @Transactional(readOnly = true, rollbackFor = {RuntimeException.class, Exception.class})
    public boolean isUserExists(String uniqueId) {
        try {
            AtTiktokAuthorPoolDO existing = atTtAuthorPoolBiz.queryAuthorPoolByUniqueId(uniqueId);
            return existing != null;

        } catch (Exception e) {
            log.error("检查TikTok用户是否存在异常，uniqueId: {}", uniqueId, e);
            return false;
        }
    }

    @Transactional(readOnly = true, rollbackFor = {RuntimeException.class, Exception.class})
    public List<AtTiktokAuthorPoolDO> queryTxNeedToUpdate(){
        List<AtTiktokAuthorPoolDO> list = atTtAuthorPoolBiz.queryUpdateNext();
        return list;
    }

    /**
     * 获取用户信息
     */
    public AtTiktokAuthorPoolDO getUserProfile(String uniqueId) {
        try {
            return atTtAuthorPoolBiz.queryAuthorPoolByUniqueId(uniqueId);

        } catch (Exception e) {
            log.error("获取TikTok用户信息异常，uniqueId: {}", uniqueId, e);
            return null;
        }
    }

}
