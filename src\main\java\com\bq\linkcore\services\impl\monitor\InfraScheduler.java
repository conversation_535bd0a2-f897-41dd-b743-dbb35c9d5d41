package com.bq.linkcore.services.impl.monitor;

import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.services.impl.monitor.core.MonitoringCoreService;
import com.bq.linkcore.services.impl.monitor.provider.tiktok.TiktokDataStorageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 每日任务触发器: 调度作品，等数据的更新
 *
 * <AUTHOR>
 * @date 2025-07-15
 */

@Slf4j
@Component
public class InfraScheduler {

    @Resource
    private MonitoringCoreService monitoringCoreService;

    @Resource
    private TiktokDataStorageHelper tiktokDataStorageHelper;

    @Scheduled(cron = "0 0 2 * * ?")
    public void tiktokMonitoringTask() {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        log.info("开始执行TikTok监控任务，时间: {}", currentTime);
        
        try {
            List<String> tiktokUsers = tiktokDataStorageHelper.queryTxNeedToUpdate()
                    .stream()
                    .map(AtTiktokAuthorPoolDO::getUniqueId)
                    .collect(Collectors.toList());
            
            monitoringCoreService.executeMonitoringTask("tiktok", tiktokUsers);
            
            log.info("TikTok监控任务提交完成，时间: {}", currentTime);
            
        } catch (Exception e) {
            log.error("TikTok监控任务执行异常", e);
        }
    }

    /**
     * 每日下午2点执行作品监控任务
     */
    @Scheduled(cron = "0 0 14 * * ?")
    public void dailyWorkMonitoringTask() {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        log.info("开始执行每日作品监控任务，时间: {}", currentTime);
        
        try {
//            monitoringCoreService.executeWorkMonitoringTask("all", testUsers);
            
            log.info("每日作品监控任务提交完成，时间: {}", currentTime);
            
        } catch (Exception e) {
            log.error("每日作品监控任务执行异常", e);
        }
    }
}
